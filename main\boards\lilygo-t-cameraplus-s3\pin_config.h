/*
 * @Description: None
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>_L
 * @Date: 2024-11-11 11:36:49
 * @LastEditTime: 2025-06-03 17:37:08
 * @License: GPL 3.0
 */
#pragma once

#ifdef CONFIG_BOARD_TYPE_LILYGO_T_CAMERAPLUS_S3_V1_0_V1_1
#define T_CameraPlus_S3_V1_0_V1_1
#elif defined CONFIG_BOARD_TYPE_LILYGO_T_CAMERAPLUS_S3_V1_2
#define T_CameraPlus_S3_V1_2
#endif

#ifdef T_CameraPlus_S3_V1_0_V1_1

// SPI
#define SPI_SCLK 36
#define SPI_MOSI 35
#define SPI_MISO 37

// IIC
#define IIC_SDA 1
#define IIC_SCL 2

// MSM261
#define MSM261_BCLK 18
#define MSM261_WS 39
#define MSM261_DATA 40

// MAX98357A
#define MAX98357A_DATA 38

// FP-133H01D
#define LCD_CS 34
#define LCD_RST 33

// OV2640
#define OV2640_PWDN -1
#define OV2640_RESET 3
#define OV2640_VSYNC 4

// CST816
#define TP_RST 48

// SY6970
#define SY6970_INT 47

#endif

#ifdef T_CameraPlus_S3_V1_2

// SPI
#define SPI_SCLK 35
#define SPI_MOSI 34
#define SPI_MISO 48

// IIC
#define IIC_SDA 33
#define IIC_SCL 37

// MP34DT05TR
#define MP34DT05TR_LRCLK 40
#define MP34DT05TR_DATA 38

#define MP34DT05TR_MAX98357_EN 18

// MAX98357A
#define MAX98357A_DATA 39

// FP-133H01D
#define LCD_CS 36
#define LCD_RST -1

// OV2640
#define OV2640_PWDN 4
#define OV2640_RESET -1
#define OV2640_VSYNC 3

// CST816
#define TP_RST -1

#endif

// SD
#define SD_CS 21
#define SD_SCLK SPI_SCLK
#define SD_MOSI SPI_MOSI
#define SD_MISO SPI_MISO

// MAX98357A
#define MAX98357A_BCLK 41
#define MAX98357A_LRCLK 42

// FP-133H01D
#define LCD_WIDTH 240
#define LCD_HEIGHT 240
#define LCD_BL 46
#define LCD_MOSI SPI_MOSI
#define LCD_SCLK SPI_SCLK
#define LCD_DC 45

// SY6970
#define SY6970_SDA IIC_SDA
#define SY6970_SCL IIC_SCL
#define SY6970_ADDRESS 0x6A

// OV2640
#define OV2640_XCLK 7
#define OV2640_SDA 1
#define OV2640_SCL 2
#define OV2640_D9 6
#define OV2640_D8 8
#define OV2640_D7 9
#define OV2640_D6 11
#define OV2640_D5 13
#define OV2640_D4 15
#define OV2640_D3 14
#define OV2640_D2 12
#define OV2640_HREF 5
#define OV2640_PCLK 10

#define PWDN_GPIO_NUM OV2640_PWDN
#define RESET_GPIO_NUM OV2640_RESET
#define XCLK_GPIO_NUM OV2640_XCLK
#define SIOD_GPIO_NUM OV2640_SDA
#define SIOC_GPIO_NUM OV2640_SCL

#define Y9_GPIO_NUM OV2640_D9
#define Y8_GPIO_NUM OV2640_D8
#define Y7_GPIO_NUM OV2640_D7
#define Y6_GPIO_NUM OV2640_D6
#define Y5_GPIO_NUM OV2640_D5
#define Y4_GPIO_NUM OV2640_D4
#define Y3_GPIO_NUM OV2640_D3
#define Y2_GPIO_NUM OV2640_D2
#define VSYNC_GPIO_NUM OV2640_VSYNC
#define HREF_GPIO_NUM OV2640_HREF
#define PCLK_GPIO_NUM OV2640_PCLK

#define XCLK_FREQ_HZ 20000000

// CST816
#define CST816_ADDRESS 0x15
#define TP_SDA IIC_SDA
#define TP_SCL IIC_SCL
#define TP_INT 47

// AP1511B
#define AP1511B_FBC 16

// KEY
#define KEY1 17
