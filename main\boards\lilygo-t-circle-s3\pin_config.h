/*
 * @Description: None
 * @Author: LILYGO_L
 * @Date: 2023-08-16 14:24:03
 * @LastEditTime: 2025-01-20 10:11:16
 * @License: GPL 3.0
 */
#pragma once

// MAX98357A
#define MAX98357A_BCLK 5
#define MAX98357A_LRCLK 4
#define MAX98357A_DATA 6
#define MAX98357A_SD_MODE 45

// MSM261
#define MSM261_BCLK 7
#define MSM261_WS 9
#define MSM261_DATA 8

// APA102
#define APA102_DATA 38
#define APA102_CLOCK 39

// H0075Y002-V0
#define LCD_WIDTH 160
#define LCD_HEIGHT 160
#define LCD_MOSI 17
#define LCD_SCLK 15
#define LCD_DC 16
#define LCD_RST -1
#define LCD_CS 13
#define LCD_BL 18

// IIC
#define IIC_SDA 11
#define IIC_SCL 14

// CST816D
#define TP_SDA 11
#define TP_SCL 14
#define TP_RST -1
#define TP_INT 12

//Rotary Encoder
#define KNOB_DATA_A 47
#define KNOB_DATA_B 48
