# ESP32-C3回声消除(AEC)方案

## 目录
1. [ESP32-C3算法实现AEC](#1-esp32-c3算法实现aec)
2. [PCB硬件AEC芯片模块](#2-pcb硬件aec芯片模块)
3. [服务端AEC（时间戳对齐）](#3-服务端aec时间戳对齐)
4. [三个案例](#4-三种方案)

---

## 1. ESP32-C3算法实现AEC

### 1.1 ESP32-C3硬件特性分析

ESP32-C3作为单核RISC-V架构的微控制器，在实现AEC算法时面临以下挑战和优势：

**硬件规格：**
- CPU: 单核RISC-V 32位，最高160MHz
- RAM: 400KB SRAM

**资源限制：**
- 内存紧张：可用于AEC的RAM约50-100KB
- 计算能力有限：需要高度优化的算法
- 实时性要求：音频处理延迟需控制在20ms以内

### 1.2 算法选择与对比

#### 1.2.1 WebRTC AECM 

WebRTC AECM是专为移动设备设计的轻量级回声消除算法：

**技术特点：**
- 内存占用：约20KB
- CPU占用：约40% @ 160MHz（提到AECM在192M主频芯片上运行时会占用40%的CPU和20k内存）
- 支持采样率：8kHz/16kHz
- 算法复杂度：中等
- 效果质量：良好

**核心算法原理：**
```
AECM采用频域处理方式：
1. 时域信号 → FFT变换 → 频域处理
2. 自适应滤波器估计回声路径
3. 频域回声抑制
4. IFFT变换 → 时域输出信号
```


#### 1.2.2 简化NLMS算法

对于极度资源受限的场景，可以使用简化的NLMS（归一化最小均方）算法：

**优势：**
- 内存占用：2-4KB
- CPU占用：5-10% @ 160MHz
- 实现简单，易于调试

**算法实现：**
```c
typedef struct {
    float weights[NLMS_FILTER_LENGTH];     // 滤波器系数
    float input_buffer[NLMS_FILTER_LENGTH]; // 输入缓冲区
    float step_size;                       // 步长因子
    float regularization;                  // 正则化参数
    int buffer_index;                      // 缓冲区索引
} nlms_aec_t;

// NLMS核心处理函数
float nlms_process_sample(nlms_aec_t* aec, float near_end, float far_end) {
    // 更新输入缓冲区
    aec->input_buffer[aec->buffer_index] = far_end;
    
    // 计算滤波器输出（回声估计）
    float echo_estimate = 0.0f;
    for (int i = 0; i < NLMS_FILTER_LENGTH; i++) {
        int idx = (aec->buffer_index - i + NLMS_FILTER_LENGTH) % NLMS_FILTER_LENGTH;
        echo_estimate += aec->weights[i] * aec->input_buffer[idx];
    }
    
    // 计算误差信号
    float error = near_end - echo_estimate;
    
    // 计算输入功率
    float input_power = aec->regularization;
    for (int i = 0; i < NLMS_FILTER_LENGTH; i++) {
        float sample = aec->input_buffer[i];
        input_power += sample * sample;
    }
    
    // 更新滤波器系数
    float normalized_step = aec->step_size / input_power;
    for (int i = 0; i < NLMS_FILTER_LENGTH; i++) {
        int idx = (aec->buffer_index - i + NLMS_FILTER_LENGTH) % NLMS_FILTER_LENGTH;
        aec->weights[i] += normalized_step * error * aec->input_buffer[idx];
    }
    
    // 更新缓冲区索引
    aec->buffer_index = (aec->buffer_index + 1) % NLMS_FILTER_LENGTH;
    
    return error; // 返回回声消除后的信号
}
```

### 1.3 内存优化策略

#### 1.3.1 内存布局优化

```c
// 优化的内存分配策略
#define AEC_STATIC_MEMORY_SIZE  (12 * 1024)  // 12KB静态内存池

// 使用内存池避免动态分配
static uint8_t aec_memory_pool[AEC_STATIC_MEMORY_SIZE];
static size_t memory_pool_offset = 0;

void* aec_malloc(size_t size) {
    if (memory_pool_offset + size > AEC_STATIC_MEMORY_SIZE) {
        return NULL; // 内存不足
    }
    void* ptr = &aec_memory_pool[memory_pool_offset];
    memory_pool_offset += (size + 3) & ~3; // 4字节对齐
    return ptr;
}
```

#### 1.3.2 数据类型优化

```c
// 使用定点数代替浮点数
typedef int32_t fixed_point_t;
#define FIXED_POINT_SHIFT 16
#define FLOAT_TO_FIXED(x) ((fixed_point_t)((x) * (1 << FIXED_POINT_SHIFT)))
#define FIXED_TO_FLOAT(x) ((float)(x) / (1 << FIXED_POINT_SHIFT))

// 定点数乘法
static inline fixed_point_t fixed_multiply(fixed_point_t a, fixed_point_t b) {
    return (fixed_point_t)(((int64_t)a * b) >> FIXED_POINT_SHIFT);
}
```

### 1.4 性能优化技术

#### 1.4.1 RISC-V汇编优化

对于关键的内积运算，可以使用RISC-V汇编优化：

```c
// 优化的向量内积运算
static inline int32_t vector_dot_product_asm(const int16_t* a, const int16_t* b, int length) {
    int32_t result = 0;
    
    // 使用RISC-V汇编优化
    asm volatile (
        "li %0, 0\n"                    // result = 0
        "1:\n"
        "beqz %3, 2f\n"                 // if (length == 0) goto end
        "lh t0, 0(%1)\n"                // load a[i]
        "lh t1, 0(%2)\n"                // load b[i]
        "mul t0, t0, t1\n"              // multiply
        "add %0, %0, t0\n"              // accumulate
        "addi %1, %1, 2\n"              // a++
        "addi %2, %2, 2\n"              // b++
        "addi %3, %3, -1\n"             // length--
        "j 1b\n"                        // loop
        "2:\n"
        : "=&r" (result), "+r" (a), "+r" (b), "+r" (length)
        :
        : "t0", "t1"
    );
    
    return result;
}
```

#### 1.4.2 循环展开优化

```c
// 循环展开优化的FIR滤波器
static inline float fir_filter_optimized(const float* coeffs, const float* samples, int length) {
    float result = 0.0f;
    int i;
    
    // 4路循环展开
    for (i = 0; i < length - 3; i += 4) {
        result += coeffs[i] * samples[i] +
                  coeffs[i+1] * samples[i+1] +
                  coeffs[i+2] * samples[i+2] +
                  coeffs[i+3] * samples[i+3];
    }
    
    // 处理剩余元素
    for (; i < length; i++) {
        result += coeffs[i] * samples[i];
    }
    
    return result;
}
```

### 1.5 实际集成实现

#### 1.5.1 ESP32-C3 AECM完整实现

```c
// esp32c3_aecm.h
#ifndef ESP32C3_AECM_H
#define ESP32C3_AECM_H

#include <stdint.h>
#include <stdbool.h>

// 配置参数
#define AECM_SAMPLE_RATE        16000
#define AECM_FRAME_SIZE         160     // 10ms @ 16kHz
#define AECM_FILTER_LENGTH      64      // 4ms回声路径
#define AECM_MAX_DELAY_MS       50      // 最大延迟50ms

typedef struct esp32c3_aecm_s esp32c3_aecm_t;

// API接口
esp32c3_aecm_t* esp32c3_aecm_create(void);
void esp32c3_aecm_destroy(esp32c3_aecm_t* aecm);
int esp32c3_aecm_process_frame(esp32c3_aecm_t* aecm,
                               const int16_t* near_end,
                               const int16_t* far_end,
                               int16_t* output);
void esp32c3_aecm_reset(esp32c3_aecm_t* aecm);

#endif
```

#### 1.5.2 集成到小智项目

在小智项目中集成AECM的步骤：

```cpp
// 在audio_processing目录下创建esp32c3_aec_processor.h
class Esp32C3AecProcessor : public AudioProcessor {
public:
    Esp32C3AecProcessor();
    virtual ~Esp32C3AecProcessor();
    
    bool Initialize(int sample_rate, int channels) override;
    void ProcessAudio(const int16_t* input, int16_t* output, size_t frames) override;
    void SetReferenceAudio(const int16_t* reference, size_t frames) override;
    
private:
    esp32c3_aecm_t* aecm_handle_;
    std::vector<int16_t> reference_buffer_;
    bool initialized_;
};
```

### 1.6 性能测试与调优

#### 1.6.1 性能基准测试

```c
// 性能测试代码
void aec_performance_test(void) {
    const int test_frames = 1000;
    int16_t near_end[AECM_FRAME_SIZE];
    int16_t far_end[AECM_FRAME_SIZE];
    int16_t output[AECM_FRAME_SIZE];
    
    esp32c3_aecm_t* aecm = esp32c3_aecm_create();
    
    uint32_t start_time = esp_timer_get_time();
    
    for (int i = 0; i < test_frames; i++) {
        esp32c3_aecm_process_frame(aecm, near_end, far_end, output);
    }
    
    uint32_t end_time = esp_timer_get_time();
    uint32_t total_time_us = end_time - start_time;
    
    printf("AEC Performance Test Results:\n");
    printf("Total frames: %d\n", test_frames);
    printf("Total time: %lu us\n", total_time_us);
    printf("Average time per frame: %.2f us\n", (float)total_time_us / test_frames);
    printf("Real-time factor: %.2f\n", 
           (float)(test_frames * AECM_FRAME_SIZE * 1000000) / 
           (AECM_SAMPLE_RATE * total_time_us));
    
    esp32c3_aecm_destroy(aecm);
}
```

#### 1.6.2 内存使用分析

```c
// 内存使用统计
void aec_memory_analysis(void) {
    size_t free_heap_before = esp_get_free_heap_size();
    
    esp32c3_aecm_t* aecm = esp32c3_aecm_create();
    
    size_t free_heap_after = esp_get_free_heap_size();
    size_t aec_memory_usage = free_heap_before - free_heap_after;
    
    printf("AEC Memory Usage Analysis:\n");
    printf("Free heap before: %zu bytes\n", free_heap_before);
    printf("Free heap after: %zu bytes\n", free_heap_after);
    printf("AEC memory usage: %zu bytes\n", aec_memory_usage);
    
    esp32c3_aecm_destroy(aecm);
}
```

---

## 2. PCB硬件AEC芯片模块

### 2.1 专用AEC芯片方案

*（此章节内容待后续填充）*

### 2.2 硬件集成设计

*（此章节内容待后续填充）*

### 2.3 成本效益分析

*（此章节内容待后续填充）*

---

## 3. 服务端AEC（时间戳对齐）

### 3.1 时间戳同步机制

*（此章节内容待后续填充）*

### 3.2 延迟补偿算法

*（此章节内容待后续填充）*

### 3.3 网络传输优化

*（此章节内容待后续填充）*

---

## 4. 三种方案对比分析

### 4.1 技术对比

*（此章节内容待后续填充）*

### 4.2 应用场景分析

*（此章节内容待后续填充）*

### 4.3 实际案例研究

*（此章节内容待后续填充）*
